import React from 'react';
import { use<PERSON>ara<PERSON>, useN<PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Screen } from '@/app-components/layout/screen';
import {
  ArrowLeft,
  Edit,
  Printer as PrinterIcon,
  Wifi,
  WifiOff,
  TestTube,
  Settings,
  MapPin,
  Monitor,
  Receipt,
  Utensils,
  Wine,
  FileText
} from 'lucide-react';
import {
  useRetrievePrinterQuery,
  useDeletePrinterMutation,
  usePatchPrinterMutation
} from '@/redux/slices/printers';
import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';
import { PrinterType, PrinterInterfaceType } from '@/types/pos';

const PrinterDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const { data: printer, isLoading, error, refetch } = useRetrievePrinterQuery(id || '');
  const [deletePrinter] = useDeletePrinterMutation();
  const [patchPrinter] = usePatchPrinterMutation();

  const handleDelete = async () => {
    if (!id) return;
    
    if (window.confirm('Are you sure you want to delete this printer? This action cannot be undone.')) {
      try {
        await deletePrinter(id).unwrap();
        handleApiSuccess('Printer deleted successfully');
        navigate('/pos/printers');
      } catch (error) {
        handleApiError(error, 'delete printer');
      }
    }
  };

  const handleToggleActivation = async () => {
    if (!id || !printer) return;
    
    const action = printer.is_active ? 'deactivate' : 'activate';
    if (window.confirm(`Are you sure you want to ${action} this printer?`)) {
      try {
        await patchPrinter({
          id,
          data: { is_active: !printer.is_active }
        }).unwrap();
        handleApiSuccess(`Printer ${action}d successfully`);
        refetch();
      } catch (error) {
        handleApiError(error, `${action} printer`);
      }
    }
  };

  const getPrinterTypeIcon = (type?: string) => {
    switch (type) {
      case 'receipt':
        return <Receipt className="h-4 w-4" />;
      case 'kitchen':
        return <Utensils className="h-4 w-4" />;
      case 'bar':
        return <Wine className="h-4 w-4" />;
      case 'bill':
        return <FileText className="h-4 w-4" />;
      default:
        return <PrinterIcon className="h-4 w-4" />;
    }
  };

  const getInterfaceIcon = (interfaceType?: string) => {
    switch (interfaceType) {
      case 'WIFI':
      case 'Wi-Fi':
        return <Wifi className="h-4 w-4" />;
      case 'LAN':
      case 'IP':
        return <Monitor className="h-4 w-4" />;
      default:
        return <WifiOff className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex justify-center items-center h-64">
          <div className="text-muted-foreground">Loading printer details...</div>
        </div>
      </Screen>
    );
  }

  if (error || !printer) {
    return (
      <Screen>
        <div className="flex justify-center items-center h-64">
          <div className="text-red-600">Error loading printer details. Please try again.</div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-4xl">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate('/pos/printers')}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
                {getPrinterTypeIcon(printer.printer_purpose)}
                {printer.name}
              </h1>
              <p className="text-muted-foreground">
                Printer Configuration Details
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleToggleActivation}>
              {printer.is_active ? 'Deactivate' : 'Activate'}
            </Button>
            <Link to={`/pos/printers/${id}/edit`}>
              <Button variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </Link>
            <Button variant="destructive" onClick={handleDelete}>
              Delete
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Core printer configuration details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Printer Code</label>
                  <p className="text-sm">{printer.printer_code}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Name</label>
                  <p className="text-sm">{printer.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Type</label>
                  <div className="flex items-center gap-2">
                    {getPrinterTypeIcon(printer.printer_purpose)}
                    <Badge variant="outline">
                      {printer.printer_purpose || 'Not specified'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Interface</label>
                  <div className="flex items-center gap-2">
                    {getInterfaceIcon(printer.interface_type)}
                    <Badge variant="outline">
                      {printer.interface_type || 'Not specified'}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Network Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Network Configuration</CardTitle>
              <CardDescription>
                Network and connection settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">IP Address</label>
                  <p className="text-sm font-mono">{printer.ip_address || 'Not configured'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Port</label>
                  <p className="text-sm font-mono">{printer.port || 'Default'}</p>
                </div>
              </div>
              {printer.location_note && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Location Note</label>
                  <p className="text-sm">{printer.location_note}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Status Information */}
          <Card>
            <CardHeader>
              <CardTitle>Status & Settings</CardTitle>
              <CardDescription>
                Current printer status and configuration
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Status</span>
                <Badge variant={printer.is_active ? 'default' : 'secondary'}>
                  {printer.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Backup Printer</span>
                <Badge variant={printer.is_backup_printer ? 'outline' : 'secondary'}>
                  {printer.is_backup_printer ? 'Yes' : 'No'}
                </Badge>
              </div>
              {printer.receipt_template && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Receipt Template</span>
                  <span className="text-sm">Template #{printer.receipt_template}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Printer Actions</CardTitle>
              <CardDescription>
                Test and configure printer functionality
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <TestTube className="h-4 w-4 mr-2" />
                Test Print
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Settings className="h-4 w-4 mr-2" />
                Configure Routing
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <MapPin className="h-4 w-4 mr-2" />
                Update Location
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </Screen>
  );
};

export default PrinterDetail;
